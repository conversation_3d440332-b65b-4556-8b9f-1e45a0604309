#!/usr/bin/env python3
"""
Main Analysis Script for Streamlined Measles Transmission Modeling

This script provides a command-line interface for running the complete measles
transmission modeling pipeline on user-provided data. It demonstrates how to
use all components of the streamlined methods package.

Usage:
    python main.py <data_directory> [options]

Example:
    python main.py ./my_measles_data --output ./results --region "West Africa"

The script expects the data directory to contain:
- epidemiological_data.csv: Time series of cases, births, vaccination coverage
- sia_data.csv: Supplementary immunization activity data
- survey_data.csv: Demographic and health survey data (optional)
- case_data.csv: Individual case records for classification (optional)

Paper Reference:
This script implements the complete methodology described in "Routine immunization
intensification, vaccination campaigns, and measles transmission in Southern Nigeria"
"""

import argparse
import sys
import logging
from pathlib import Path
import pandas as pd
import numpy as np
from typing import Dict, Optional

# Import streamlined methods
from . import (
    MeaslesTransmissionModel, 
    ModelInputProcessor,
    CaseClassifier,
    AgeDistributionEstimator,
    DemographicProcessor,
    DataValidator,
    ValidationError,
    ModelParameters
)


def setup_logging(log_level: str = "INFO") -> logging.Logger:
    """Set up logging configuration."""
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('measles_analysis.log')
        ]
    )
    return logging.getLogger(__name__)


def load_and_validate_data(data_dir: Path, logger: logging.Logger) -> Dict[str, pd.DataFrame]:
    """
    Load and validate all input data files.
    
    Args:
        data_dir: Directory containing input data files
        logger: Logger instance
        
    Returns:
        Dictionary of loaded DataFrames
        
    Raises:
        FileNotFoundError: If required files are missing
        ValidationError: If data validation fails
    """
    logger.info(f"Loading data from {data_dir}")
    
    # Required files
    required_files = {
        'epidemiological': 'epidemiological_data.csv',
        'sia': 'sia_data.csv'
    }
    
    # Optional files
    optional_files = {
        'survey': 'survey_data.csv',
        'cases': 'case_data.csv'
    }
    
    data = {}
    validator = DataValidator(strict_mode=False)  # Use warnings for optional validations
    
    # Load required files
    for key, filename in required_files.items():
        file_path = data_dir / filename
        if not file_path.exists():
            raise FileNotFoundError(
                f"Required file not found: {file_path}\n"
                f"Please ensure your data directory contains {filename}"
            )
        
        try:
            df = pd.read_csv(file_path, parse_dates=True, index_col=0)
            logger.info(f"Loaded {key} data: {len(df)} records")
            
            # Validate data
            if key == 'epidemiological':
                validator.validate_epidemiological_data(df)
            elif key == 'sia':
                validator.validate_sia_data(df)
            
            data[key] = df
            
        except Exception as e:
            logger.error(f"Error loading {filename}: {str(e)}")
            raise
    
    # Load optional files
    for key, filename in optional_files.items():
        file_path = data_dir / filename
        if file_path.exists():
            try:
                df = pd.read_csv(file_path, parse_dates=True)
                logger.info(f"Loaded optional {key} data: {len(df)} records")
                data[key] = df
            except Exception as e:
                logger.warning(f"Could not load optional file {filename}: {str(e)}")
        else:
            logger.info(f"Optional file {filename} not found, skipping")
    
    logger.info("Data loading completed successfully")
    logger.info(f"Validation summary:\n{validator.get_validation_summary()}")
    
    return data


def run_case_classification(case_data: pd.DataFrame, logger: logging.Logger) -> pd.DataFrame:
    """
    Run case classification analysis if case-level data is available.
    
    Paper Reference: Appendix 1 - Untested, isolated, clinically compatible cases
    """
    logger.info("Running case classification analysis...")
    
    try:
        classifier = CaseClassifier(regularization_strength=1.0)
        
        # Fit the classification model
        fit_results = classifier.fit(case_data, include_temporal=True)
        logger.info(f"Case classification model fitted: {fit_results}")
        
        # Classify all cases
        classified_cases = classifier.classify_untested_cases(case_data, threshold=0.9)
        
        # Log classification summary
        classification_summary = classified_cases['final_classification'].value_counts()
        logger.info(f"Case classification results:\n{classification_summary}")
        
        return classified_cases
        
    except Exception as e:
        logger.error(f"Case classification failed: {str(e)}")
        raise


def run_age_distribution_analysis(case_data: pd.DataFrame, logger: logging.Logger) -> Dict:
    """
    Run age distribution analysis if case-level data is available.
    
    Paper Reference: Appendix 2 - Age distribution smoothing
    """
    logger.info("Running age distribution analysis...")
    
    try:
        estimator = AgeDistributionEstimator(
            correlation_time=10.0,
            age_correlation=4.0,
            max_age=25
        )
        
        # Prepare age data
        age_counts, observation_mask = estimator.prepare_age_data(case_data)
        logger.info(f"Prepared age data: {age_counts.shape} matrix")
        
        # Fit the model
        fit_results = estimator.fit(age_counts, observation_mask)
        logger.info(f"Age distribution model fitted: {fit_results['converged']}")
        
        return {
            'estimator': estimator,
            'age_counts': age_counts,
            'fit_results': fit_results
        }
        
    except Exception as e:
        logger.error(f"Age distribution analysis failed: {str(e)}")
        raise


def run_demographic_analysis(survey_data: pd.DataFrame, logger: logging.Logger) -> Dict:
    """
    Run demographic analysis if survey data is available.
    
    Paper Reference: Demographic modeling for birth rates and vaccination coverage
    """
    logger.info("Running demographic analysis...")
    
    try:
        processor = DemographicProcessor(reference_year=2023)
        
        # This is a simplified example - real implementation would need
        # population data and proper survey structure
        logger.info("Demographic analysis requires additional population data")
        logger.info("See demographic_analysis.py for full implementation details")
        
        return {'processor': processor}
        
    except Exception as e:
        logger.error(f"Demographic analysis failed: {str(e)}")
        raise


def run_transmission_model(epi_data: pd.DataFrame, 
                          sia_data: pd.DataFrame,
                          logger: logging.Logger) -> Dict:
    """
    Run the core transmission model analysis.
    
    Paper Reference: Main methodology - SIR transmission model
    """
    logger.info("Running transmission model analysis...")
    
    try:
        # Set up model parameters
        params = ModelParameters(
            beta_correlation=3.0,
            tau=26,  # Semi-monthly periods
            mcv1_efficacy=0.825,
            mcv2_efficacy=0.95,
            sia_efficacy_guess=0.1
        )
        
        # Initialize model
        model = MeaslesTransmissionModel(parameters=params)
        
        # Process inputs
        processor = ModelInputProcessor(parameters=params)
        processed_epi = processor.process_epidemiological_data(epi_data)
        
        logger.info(f"Processed epidemiological data: {len(processed_epi)} time points")
        
        # Estimate initial susceptibility (simplified approach)
        # In practice, this would come from survival analysis as in the paper
        initial_susceptibility = processed_epi['adj_births'].sum() * 0.1  # 10% of adjusted births
        susceptibility_variance = initial_susceptibility * 0.5  # 50% uncertainty
        
        logger.info(f"Initial susceptibility estimate: {initial_susceptibility:.0f} ± {np.sqrt(susceptibility_variance):.0f}")
        
        # Fit the model
        results = model.fit(
            epidemiological_data=processed_epi,
            sia_data=sia_data,
            initial_susceptibility=initial_susceptibility,
            susceptibility_variance=susceptibility_variance
        )
        
        logger.info(f"Transmission model fitted successfully: {results.convergence_success}")
        logger.info(f"Estimated SIA efficacies: {results.sia_efficacy}")
        
        return {
            'model': model,
            'results': results,
            'processed_data': processed_epi
        }
        
    except Exception as e:
        logger.error(f"Transmission model analysis failed: {str(e)}")
        raise


def save_results(results: Dict, output_dir: Path, logger: logging.Logger):
    """Save analysis results to output directory."""
    logger.info(f"Saving results to {output_dir}")
    
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Save transmission model results
    if 'transmission' in results:
        transmission_results = results['transmission']['results']
        
        # Save SIA efficacy estimates
        sia_results = pd.DataFrame({
            'sia_efficacy': transmission_results.sia_efficacy,
            'sia_efficacy_variance': transmission_results.sia_efficacy_variance
        })
        sia_results.to_csv(output_dir / 'sia_efficacy_estimates.csv')
        
        # Save trajectories
        transmission_results.susceptibility_trajectory.to_csv(
            output_dir / 'susceptibility_trajectory.csv'
        )
        transmission_results.infection_trajectory.to_csv(
            output_dir / 'infection_trajectory.csv'
        )
    
    # Save case classification results
    if 'case_classification' in results:
        results['case_classification'].to_csv(
            output_dir / 'classified_cases.csv', index=False
        )
    
    # Save age distribution results
    if 'age_distribution' in results:
        age_results = results['age_distribution']
        age_results['age_counts'].to_csv(output_dir / 'age_distribution_counts.csv')
    
    logger.info("Results saved successfully")


def main():
    """Main analysis pipeline."""
    parser = argparse.ArgumentParser(
        description="Streamlined Measles Transmission Modeling Analysis",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
    python main.py ./data --output ./results
    python main.py ./nigeria_data --output ./nigeria_results --log-level DEBUG
    
Data Directory Structure:
    data/
    ├── epidemiological_data.csv  (required)
    ├── sia_data.csv              (required)
    ├── survey_data.csv           (optional)
    └── case_data.csv             (optional)
        """
    )
    
    parser.add_argument(
        'data_dir',
        type=Path,
        help='Directory containing input data files'
    )
    
    parser.add_argument(
        '--output', '-o',
        type=Path,
        default=Path('./results'),
        help='Output directory for results (default: ./results)'
    )
    
    parser.add_argument(
        '--log-level',
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
        default='INFO',
        help='Logging level (default: INFO)'
    )
    
    parser.add_argument(
        '--region',
        type=str,
        help='Region name for analysis (for documentation purposes)'
    )
    
    args = parser.parse_args()
    
    # Set up logging
    logger = setup_logging(args.log_level)
    
    try:
        logger.info("Starting measles transmission modeling analysis")
        if args.region:
            logger.info(f"Analysis region: {args.region}")
        
        # Load and validate data
        data = load_and_validate_data(args.data_dir, logger)
        
        # Run analyses
        analysis_results = {}
        
        # 1. Case classification (if case data available)
        if 'cases' in data:
            analysis_results['case_classification'] = run_case_classification(
                data['cases'], logger
            )
        
        # 2. Age distribution analysis (if case data available)
        if 'cases' in data:
            analysis_results['age_distribution'] = run_age_distribution_analysis(
                data['cases'], logger
            )
        
        # 3. Demographic analysis (if survey data available)
        if 'survey' in data:
            analysis_results['demographic'] = run_demographic_analysis(
                data['survey'], logger
            )
        
        # 4. Core transmission model (always run)
        analysis_results['transmission'] = run_transmission_model(
            data['epidemiological'], data['sia'], logger
        )
        
        # Save results
        save_results(analysis_results, args.output, logger)
        
        logger.info("Analysis completed successfully!")
        logger.info(f"Results saved to: {args.output}")
        
    except Exception as e:
        logger.error(f"Analysis failed: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()

# Streamlined Measles Transmission Modeling - Requirements
# Compatible with Python 3.10+

# Core scientific computing
numpy>=1.21.0
pandas>=1.3.0
scipy>=1.7.0

# Machine learning and statistics
scikit-learn>=1.0.0

# Plotting and visualization
matplotlib>=3.5.0

# Optional: Enhanced data processing
# openpyxl>=3.0.0  # For Excel file support
# seaborn>=0.11.0  # For enhanced plotting

# Development and testing (optional)
# pytest>=6.0.0
# pytest-cov>=2.0.0
# black>=21.0.0
# flake8>=3.9.0

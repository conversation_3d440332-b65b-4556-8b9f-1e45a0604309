# Streamlined Measles Transmission Modeling Methods

A modernized, user-friendly implementation of the measles transmission modeling methods described in:

> **"Routine immunization intensification, vaccination campaigns, and measles transmission in Southern Nigeria"** (2025)  
> <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>

This package provides researchers with accessible tools to apply these methods to their own measles surveillance and vaccination data.

## 🎯 What This Package Does

This package implements the complete methodology for:

1. **Transmission Model Analysis**: Core SIR model to estimate vaccination campaign efficacy
2. **Case Classification**: Logistic regression to classify untested clinical cases  
3. **Age Distribution Modeling**: Bayesian smoothing of age-at-infection patterns
4. **Demographic Analysis**: Survey data processing for birth rates and vaccination coverage

**Key Finding from Original Paper**: Age-targeted vaccination efforts (like Nigeria's 2019 intensification) can be **more than twice as effective per dose** compared to mass campaigns.

## 🚀 Quick Start

### Installation Requirements

- Python 3.10 or higher
- Required packages: `numpy`, `pandas`, `scipy`, `scikit-learn`

```bash
pip install numpy pandas scipy scikit-learn matplotlib
```

### Basic Usage

```bash
# Run complete analysis on your data
python main.py /path/to/your/data --output ./results

# With additional options
python main.py ./my_data --output ./my_results --region "My Region" --log-level DEBUG
```

### Python API Usage

```python
from methods_streamlined import MeaslesTransmissionModel, ModelParameters

# Set up model parameters
params = ModelParameters(
    mcv1_efficacy=0.825,  # MCV1 vaccine efficacy
    mcv2_efficacy=0.95,   # MCV2 vaccine efficacy
    sia_efficacy_guess=0.1  # Initial guess for campaign efficacy
)

# Initialize and fit model
model = MeaslesTransmissionModel(parameters=params)
results = model.fit(
    epidemiological_data=epi_df,
    sia_data=sia_df,
    initial_susceptibility=1000,
    susceptibility_variance=500
)

# Access results
print(f"SIA efficacies: {results.sia_efficacy}")
print(f"Model converged: {results.convergence_success}")
```

## 📊 Data Format Requirements

### Required Files

#### 1. `epidemiological_data.csv`
Time series data with DatetimeIndex and columns:

| Column | Description | Units | Example |
|--------|-------------|-------|---------|
| `cases` | Confirmed/probable measles cases | Count | 45 |
| `births` | Number of births | Count | 1200 |
| `mcv1` | MCV1 vaccination coverage | Proportion (0-1) | 0.75 |
| `mcv2` | MCV2 vaccination coverage | Proportion (0-1) | 0.65 |

```csv
date,cases,births,mcv1,mcv2
2020-01-01,23,1150,0.78,0.62
2020-01-15,31,1200,0.79,0.63
2020-02-01,18,1180,0.77,0.61
```

#### 2. `sia_data.csv`
Supplementary Immunization Activity data with DatetimeIndex:

| Column | Description | Units |
|--------|-------------|-------|
| `campaign_2018` | Doses delivered in 2018 campaign | Count |
| `campaign_2020` | Doses delivered in 2020 campaign | Count |
| `iri_2019` | Doses in 2019 intensification | Count |

```csv
date,campaign_2018,iri_2019,campaign_2020
2020-01-01,0,0,0
2020-01-15,0,0,0
2020-03-01,0,0,125000
```

### Optional Files

#### 3. `case_data.csv` (for case classification)
Individual case records:

| Column | Description | Example |
|--------|-------------|---------|
| `age` | Age in years | 2.5 |
| `vaccine_doses` | Number of vaccine doses | 1 |
| `lab_result` | Laboratory result | "confirmed", "rejected", or NaN |
| `date` | Case date | "2020-03-15" |

#### 4. `survey_data.csv` (for demographic analysis)
Household survey data (DHS/MICS format):

| Column | Description |
|--------|-------------|
| `birth_date` | Child birth date |
| `vaccination_date` | MCV1 vaccination date |
| `mother_weight` | Survey weight |
| `state` | Geographic unit |

## 🔧 Model Components

### 1. Transmission Model (`transmission_model.py`)

**Paper Reference**: Main methodology, Appendix 2 (Equations 3-4)

Implements the core SIR transmission model that estimates:
- Population susceptibility over time
- Vaccination campaign efficacy (key outcome)
- Transmission seasonality patterns
- Surveillance under-reporting rates

```python
from methods_streamlined import MeaslesTransmissionModel

model = MeaslesTransmissionModel()
results = model.fit(epi_data, sia_data, initial_S=1000, S_var=500)
```

### 2. Case Classification (`case_classification.py`)

**Paper Reference**: Appendix 1 - "Untested, isolated, clinically compatible cases"

Logistic regression to estimate probability that untested clinical cases are true measles:

```python
from methods_streamlined import CaseClassifier

classifier = CaseClassifier()
classifier.fit(case_data)
classified_cases = classifier.classify_untested_cases(case_data, threshold=0.9)
```

### 3. Age Distribution (`age_distribution.py`)

**Paper Reference**: Appendix 2 - Age distribution smoothing

Bayesian smoothing of age-at-infection patterns:

```python
from methods_streamlined import AgeDistributionEstimator

estimator = AgeDistributionEstimator(correlation_time=10.0)
age_counts, mask = estimator.prepare_age_data(case_data)
results = estimator.fit(age_counts, mask)
```

### 4. Demographic Analysis (`demographic_analysis.py`)

**Paper Reference**: Survey data processing methodology

Processes DHS/MICS data to estimate birth rates and vaccination coverage:

```python
from methods_streamlined import DemographicProcessor

processor = DemographicProcessor()
birth_estimates = processor.process_birth_data(survey_data, population_data)
coverage_estimates = processor.process_vaccination_data(survey_data)
```

## ⚠️ Common Issues and Solutions

### Data Validation Errors

**Error**: `ValidationError: Missing required columns: ['cases', 'births']`
- **Solution**: Ensure your epidemiological data has the exact column names
- **Check**: Column spelling, case sensitivity, extra spaces

**Error**: `ValidationError: Data must have a DatetimeIndex`
- **Solution**: Convert your date column and set as index:
```python
df['date'] = pd.to_datetime(df['date'])
df = df.set_index('date')
```

**Error**: `ValidationError: Negative values found in cases column`
- **Solution**: Check if missing data is coded as negative values
- **Fix**: Replace negative values with 0 or NaN as appropriate

### Model Fitting Issues

**Warning**: `Model did not converge after 50 iterations`
- **Cause**: Insufficient data or poor initial conditions
- **Solutions**: 
  - Check data quality and completeness
  - Adjust initial susceptibility estimates
  - Verify SIA data alignment with epidemiological data

**Error**: `RuntimeError: Model fitting failed`
- **Debug steps**:
  1. Check data validation summary
  2. Verify time series alignment
  3. Ensure SIA data has reasonable values
  4. Check for missing time periods

### Memory and Performance

**Issue**: Large datasets causing memory errors
- **Solution**: Process data in chunks or reduce time resolution
- **Alternative**: Use monthly instead of semi-monthly aggregation

**Issue**: Slow model fitting
- **Cause**: Large number of SIA campaigns or long time series
- **Solutions**: 
  - Reduce correlation parameters
  - Limit analysis to specific time periods
  - Use fewer optimization iterations

## 📈 Interpreting Results

### SIA Efficacy Estimates

The key output is `results.sia_efficacy` - the fraction of SIA doses that immunized a susceptible individual:

- **0.05 (5%)**: Typical for mass campaigns in the original study
- **0.15 (15%)**: Typical for targeted intensification efforts
- **Higher values**: More effective targeting of susceptible populations

### Model Validation

Check these indicators for model quality:

1. **Convergence**: `results.convergence_success` should be `True`
2. **Log-likelihood**: Higher values indicate better fit
3. **Trajectory plots**: Susceptibility should decrease during campaigns
4. **Reporting rate**: Should be reasonable for your setting (0.1-10%)

## 🔬 Scientific Background

This implementation is based on the finding that **age-targeted vaccination efforts can be substantially more effective** than mass campaigns. The original study found Nigeria's 2019 routine immunization intensification (targeting 9-24 month olds) was more than twice as effective per dose compared to mass campaigns (targeting 9 months - 5 years).

**Key insights**:
- Younger children are more likely to be susceptible
- Smaller, more frequent campaigns can be more effective than large, infrequent ones
- Timing matters: campaigns should happen before high transmission seasons

## 📚 References

1. **Original Paper**: Thakkar et al. "Routine immunization intensification, vaccination campaigns, and measles transmission in Southern Nigeria" (2025)
2. **Mathematical Framework**: Anderson & May "Infectious Diseases of Humans" (1992)
3. **Bayesian Methods**: Finkenstädt & Grenfell "Time series modelling of childhood diseases" (2000)

## 🤝 Contributing

This is a research tool. If you find issues or have improvements:

1. Check the validation error messages first
2. Review the paper methodology for context
3. Test with the provided example data
4. Document any modifications for reproducibility

## 📄 License

This implementation is provided for research purposes. Please cite the original paper when using these methods.

#!/usr/bin/env python3
"""
Example Data Generator for Measles Transmission Modeling

This script generates synthetic example data that demonstrates the expected
format for the streamlined measles transmission modeling methods.

The generated data mimics the structure and patterns found in real measles
surveillance and vaccination data, but with simplified dynamics for
demonstration purposes.

Usage:
    python generate_example_data.py --output ./example_data
"""

import argparse
import numpy as np
import pandas as pd
from pathlib import Path
from datetime import datetime, timed<PERSON>ta


def generate_epidemiological_data(start_date: str = "2018-01-01", 
                                 end_date: str = "2023-12-31",
                                 frequency: str = "SMS") -> pd.DataFrame:
    """
    Generate synthetic epidemiological time series data.
    
    Creates realistic patterns including:
    - Seasonal transmission cycles
    - Vaccination campaign impacts
    - Gradual improvements in routine coverage
    """
    # Create date range
    date_range = pd.date_range(start=start_date, end=end_date, freq=frequency)
    
    # Initialize data
    data = []
    
    # Base parameters
    base_births = 1000  # births per period
    base_cases = 20     # cases per period
    
    for i, date in enumerate(date_range):
        # Seasonal patterns (higher transmission in dry season)
        seasonal_factor = 1 + 0.5 * np.sin(2 * np.pi * (date.month - 3) / 12)
        
        # Long-term trends
        year_progress = (date.year - 2018) / 5  # 5-year period
        
        # Births (slight decline over time due to demographic transition)
        births = base_births * (1 - 0.02 * year_progress) + np.random.normal(0, 50)
        births = max(0, births)
        
        # MCV1 coverage (gradual improvement)
        mcv1_coverage = 0.70 + 0.05 * year_progress + np.random.normal(0, 0.02)
        mcv1_coverage = np.clip(mcv1_coverage, 0, 1)
        
        # MCV2 coverage (introduced later, lower coverage)
        if date.year >= 2019:
            mcv2_coverage = 0.50 + 0.08 * (date.year - 2019) + np.random.normal(0, 0.02)
            mcv2_coverage = np.clip(mcv2_coverage, 0, 1)
        else:
            mcv2_coverage = 0
        
        # Cases (affected by seasonality, campaigns, and coverage)
        coverage_effect = 1 - (mcv1_coverage * 0.8 + mcv2_coverage * 0.2)
        cases = base_cases * seasonal_factor * coverage_effect + np.random.poisson(5)
        cases = max(0, cases)
        
        # Add campaign effects (dramatic reductions)
        if date.month == 3 and date.year in [2018, 2020, 2022]:  # Campaign months
            cases = cases * 0.3  # 70% reduction during campaigns
        
        data.append({
            'date': date,
            'cases': int(cases),
            'births': int(births),
            'mcv1': round(mcv1_coverage, 3),
            'mcv2': round(mcv2_coverage, 3)
        })
    
    df = pd.DataFrame(data)
    df = df.set_index('date')
    
    return df


def generate_sia_data(epidemiological_data: pd.DataFrame) -> pd.DataFrame:
    """
    Generate SIA (Supplementary Immunization Activity) data.
    
    Creates data for:
    - Mass vaccination campaigns (2018, 2020, 2022)
    - Routine immunization intensification (2019)
    """
    # Initialize with zeros
    sia_data = pd.DataFrame(
        0, 
        index=epidemiological_data.index,
        columns=['campaign_2018', 'iri_2019', 'campaign_2020', 'campaign_2022']
    )
    
    # Campaign parameters (doses delivered)
    campaign_doses = {
        'campaign_2018': 150000,  # Large mass campaign
        'iri_2019': 75000,        # Smaller, targeted intensification
        'campaign_2020': 180000,  # Large mass campaign
        'campaign_2022': 160000   # Large mass campaign
    }
    
    # Add campaign doses in specific months
    campaign_months = {
        'campaign_2018': ('2018-03-01', '2018-03-15'),
        'iri_2019': ('2019-06-01', '2019-06-15'),
        'campaign_2020': ('2020-03-01', '2020-03-15'),
        'campaign_2022': ('2022-02-01', '2022-02-15')
    }
    
    for campaign, (start_month, end_month) in campaign_months.items():
        if campaign in sia_data.columns:
            # Distribute doses across campaign period
            campaign_period = (sia_data.index >= start_month) & (sia_data.index <= end_month)
            n_periods = campaign_period.sum()
            
            if n_periods > 0:
                doses_per_period = campaign_doses[campaign] / n_periods
                sia_data.loc[campaign_period, campaign] = doses_per_period
    
    return sia_data


def generate_case_data(n_cases: int = 1000) -> pd.DataFrame:
    """
    Generate individual case data for classification analysis.
    
    Creates realistic case records with:
    - Age distribution typical of measles
    - Vaccination history
    - Laboratory test results (some missing)
    """
    np.random.seed(42)  # For reproducible example data
    
    cases = []
    
    for i in range(n_cases):
        # Age distribution (higher risk in young children)
        age = np.random.exponential(3) + 0.5  # Exponential with minimum age
        age = min(age, 15)  # Cap at 15 years
        
        # Vaccination doses (related to age and year)
        case_date = pd.Timestamp('2020-01-01') + pd.Timedelta(days=np.random.randint(0, 1095))
        
        if age < 1:
            vaccine_doses = 0
        elif age < 2:
            vaccine_doses = np.random.choice([0, 1], p=[0.3, 0.7])
        else:
            vaccine_doses = np.random.choice([0, 1, 2], p=[0.1, 0.4, 0.5])
        
        # Laboratory testing (not all cases tested)
        tested = np.random.random() < 0.7  # 70% of cases tested
        
        if tested:
            # Confirmation probability depends on age and vaccination
            base_prob = 0.6  # Base confirmation rate
            age_effect = max(0, 0.8 - age * 0.05)  # Younger more likely confirmed
            vaccine_effect = max(0.1, 1 - vaccine_doses * 0.2)  # Fewer doses more likely
            
            confirm_prob = base_prob * age_effect * vaccine_effect
            lab_result = 'confirmed' if np.random.random() < confirm_prob else 'rejected'
        else:
            lab_result = None
        
        # Birth year calculation
        birth_year = case_date.year - int(age)
        
        cases.append({
            'case_id': f'CASE_{i+1:04d}',
            'date': case_date.strftime('%Y-%m-%d'),
            'age': round(age, 1),
            'vaccine_doses': vaccine_doses if not pd.isna(vaccine_doses) else None,
            'lab_result': lab_result,
            'birth_year': birth_year
        })
    
    return pd.DataFrame(cases)


def generate_survey_data(n_households: int = 500) -> pd.DataFrame:
    """
    Generate synthetic survey data (DHS/MICS style).
    
    Creates household survey records with:
    - Birth histories
    - Vaccination records
    - Survey weights
    """
    np.random.seed(123)  # For reproducible example data
    
    surveys = []
    
    survey_years = [2018, 2020, 2022]
    states = ['State_A', 'State_B', 'State_C']
    
    for survey_year in survey_years:
        for state in states:
            n_state_households = n_households // (len(survey_years) * len(states))
            
            for i in range(n_state_households):
                # Mother characteristics
                mother_age = np.random.randint(18, 45)
                mother_education = np.random.choice(['none', 'primary', 'secondary', 'higher'], 
                                                  p=[0.2, 0.3, 0.4, 0.1])
                
                # Number of children
                n_children = np.random.poisson(2.5) + 1
                n_children = min(n_children, 6)
                
                for child_num in range(n_children):
                    # Child birth date (within last 5 years for vaccination analysis)
                    birth_year = survey_year - np.random.randint(0, 6)
                    birth_month = np.random.randint(1, 13)
                    birth_day = np.random.randint(1, 29)
                    birth_date = f"{birth_year}-{birth_month:02d}-{birth_day:02d}"
                    
                    # Child age at survey
                    child_age_months = (survey_year - birth_year) * 12 + (6 - birth_month)
                    child_age_months = max(0, child_age_months)
                    
                    # Vaccination status (if old enough)
                    if child_age_months >= 12:  # Eligible for MCV1
                        mcv1_received = np.random.random() < 0.75  # 75% coverage
                        
                        if mcv1_received and child_age_months >= 18:  # Eligible for MCV2
                            vaccination_date = pd.Timestamp(birth_date) + pd.Timedelta(days=np.random.randint(270, 400))
                        else:
                            vaccination_date = None
                    else:
                        mcv1_received = False
                        vaccination_date = None
                    
                    # Survey weight (simplified)
                    survey_weight = np.random.uniform(0.5, 2.0)
                    
                    surveys.append({
                        'household_id': f'{state}_{survey_year}_{i+1:03d}',
                        'survey_year': survey_year,
                        'state': state,
                        'birth_date': birth_date,
                        'child_age_months': child_age_months,
                        'mcv1_received': int(mcv1_received),
                        'vaccination_date': vaccination_date.strftime('%Y-%m-%d') if vaccination_date else None,
                        'mother_age': mother_age,
                        'mother_education': mother_education,
                        'survey_weight': round(survey_weight, 3)
                    })
    
    return pd.DataFrame(surveys)


def main():
    """Generate example data files."""
    parser = argparse.ArgumentParser(description="Generate example data for measles modeling")
    parser.add_argument('--output', '-o', type=Path, default=Path('./example_data'),
                       help='Output directory for example data')
    parser.add_argument('--start-date', default='2018-01-01',
                       help='Start date for time series data')
    parser.add_argument('--end-date', default='2023-12-31',
                       help='End date for time series data')
    
    args = parser.parse_args()
    
    # Create output directory
    args.output.mkdir(parents=True, exist_ok=True)
    
    print(f"Generating example data in {args.output}")
    
    # Generate epidemiological data
    print("Generating epidemiological time series...")
    epi_data = generate_epidemiological_data(args.start_date, args.end_date)
    epi_data.to_csv(args.output / 'epidemiological_data.csv')
    print(f"  Created epidemiological_data.csv ({len(epi_data)} records)")
    
    # Generate SIA data
    print("Generating SIA campaign data...")
    sia_data = generate_sia_data(epi_data)
    sia_data.to_csv(args.output / 'sia_data.csv')
    print(f"  Created sia_data.csv ({len(sia_data)} records)")
    
    # Generate case data
    print("Generating individual case data...")
    case_data = generate_case_data(1000)
    case_data.to_csv(args.output / 'case_data.csv', index=False)
    print(f"  Created case_data.csv ({len(case_data)} records)")
    
    # Generate survey data
    print("Generating survey data...")
    survey_data = generate_survey_data(500)
    survey_data.to_csv(args.output / 'survey_data.csv', index=False)
    print(f"  Created survey_data.csv ({len(survey_data)} records)")
    
    # Create a simple README for the example data
    readme_content = """# Example Data for Measles Transmission Modeling

This directory contains synthetic example data demonstrating the expected format
for the streamlined measles transmission modeling methods.

## Files:

- `epidemiological_data.csv`: Time series of cases, births, and vaccination coverage
- `sia_data.csv`: Supplementary immunization activity (campaign) data  
- `case_data.csv`: Individual case records for classification analysis
- `survey_data.csv`: Household survey data for demographic analysis

## Usage:

```bash
# Run analysis on this example data
python ../main.py . --output ./results
```

## Note:

This is synthetic data for demonstration purposes only. Real analysis should
use actual surveillance and survey data following the same format.
"""
    
    with open(args.output / 'README.md', 'w') as f:
        f.write(readme_content)
    
    print(f"\nExample data generation complete!")
    print(f"Files created in: {args.output}")
    print(f"\nTo test the analysis pipeline:")
    print(f"  cd {args.output}")
    print(f"  python ../main.py . --output ./results")


if __name__ == "__main__":
    main()

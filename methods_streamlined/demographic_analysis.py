"""
Demographic Analysis Module

Implements multiple regression with post-stratification methods for processing
Demographic and Health Survey (DHS) and Multiple Indicator Cluster Survey (MICS) data.
This module estimates key demographic parameters needed for transmission modeling.

Paper Reference:
- Main methodology: Multiple regression with post-stratification for survey analysis
- Outputs: Birth rates and routine coverage estimates (visualized in Figure 1 for Lagos)
- Data sources: DHS and MICS surveys from 2008-2021

Original code: methods/demography/ directory (multiple files)

Key estimates produced:
1. Monthly birth rates by state
2. MCV1 coverage rates over time
3. Birth seasonality patterns
4. Age-specific vaccination probabilities

These estimates are critical inputs to the transmission model, providing the
demographic foundation for understanding population susceptibility dynamics.
"""

import numpy as np
import pandas as pd
from scipy import stats
from sklearn.linear_model import LinearRegression
from sklearn.preprocessing import StandardScaler
from typing import Dict, List, Optional, Tuple, Union
import warnings

# Handle imports for both module and script execution
try:
    from .data_validation import DataValidator, ValidationError
except ImportError:
    from data_validation import DataValidator, ValidationError


class DemographicProcessor:
    """
    Processor for demographic and health survey data.
    
    This class implements the regression and post-stratification methods described
    in the paper for estimating demographic parameters from household surveys.
    The approach accounts for:
    
    - Survey design effects (weights, stratification)
    - Temporal trends in demographic rates
    - Geographic variation across states
    - Uncertainty quantification
    
    Paper Reference: Demographic modeling methodology
    Original code: Various files in methods/demography/
    """
    
    def __init__(self, reference_year: int = 2020):
        """
        Initialize the demographic processor.
        
        Args:
            reference_year: Year for standardizing estimates
        """
        self.reference_year = reference_year
        self.validator = DataValidator()
        self.fitted_models = {}
        
    def process_birth_data(self, 
                          survey_data: pd.DataFrame,
                          population_data: pd.DataFrame) -> pd.DataFrame:
        """
        Estimate monthly birth rates from survey data.
        
        Uses regression with post-stratification to estimate birth rates,
        accounting for survey design and temporal trends.
        
        Args:
            survey_data: DHS/MICS birth history data
            population_data: Population estimates by state and year
            
        Returns:
            DataFrame with monthly birth estimates by state
            
        Raises:
            ValidationError: If required columns are missing
        """
        # Validate survey data
        required_cols = ['birth_date', 'mother_weight', 'state', 'survey_year']
        missing_cols = [col for col in required_cols if col not in survey_data.columns]
        
        if missing_cols:
            raise ValidationError(
                f"Missing required columns in survey data: {missing_cols}",
                data_type="survey_birth_data",
                suggestions=[
                    "Ensure survey data includes birth_date, mother_weight, state, survey_year",
                    "Check DHS/MICS data processing pipeline",
                    "Verify column naming conventions"
                ]
            )
        
        # Convert birth dates and extract temporal features
        survey_data = survey_data.copy()
        survey_data['birth_date'] = pd.to_datetime(survey_data['birth_date'])
        survey_data['birth_year'] = survey_data['birth_date'].dt.year
        survey_data['birth_month'] = survey_data['birth_date'].dt.month
        
        # Estimate yearly births by state using weighted regression
        yearly_births = self._estimate_yearly_births(survey_data, population_data)
        
        # Estimate birth seasonality patterns
        seasonality = self._estimate_birth_seasonality(survey_data)
        
        # Combine yearly estimates with seasonality
        monthly_births = self._combine_yearly_and_seasonal(yearly_births, seasonality)
        
        return monthly_births
    
    def _estimate_yearly_births(self, 
                               survey_data: pd.DataFrame,
                               population_data: pd.DataFrame) -> pd.DataFrame:
        """
        Estimate yearly birth rates using weighted regression.
        
        Paper Reference: Regression with post-stratification methodology
        """
        # Aggregate births by state and year with survey weights
        birth_counts = survey_data.groupby(['state', 'birth_year']).agg({
            'mother_weight': 'sum',  # Weighted count of births
            'survey_year': 'first'   # For linking to population data
        }).reset_index()
        
        # Merge with population data
        birth_counts = birth_counts.merge(
            population_data, 
            on=['state', 'birth_year'], 
            how='left'
        )
        
        # Calculate crude birth rates
        birth_counts['birth_rate'] = birth_counts['mother_weight'] / birth_counts['population']
        
        # Fit temporal trend model for each state
        yearly_estimates = {}
        
        for state in birth_counts['state'].unique():
            state_data = birth_counts[birth_counts['state'] == state].copy()
            
            if len(state_data) < 3:  # Need minimum data for trend
                warnings.warn(f"Insufficient data for {state}, using regional average")
                continue
            
            # Fit linear trend
            X = state_data['birth_year'].values.reshape(-1, 1)
            y = state_data['birth_rate'].values
            weights = state_data['mother_weight'].values
            
            model = LinearRegression()
            model.fit(X, y, sample_weight=weights)
            
            # Predict for all years
            year_range = range(2008, self.reference_year + 1)
            predictions = model.predict(np.array(year_range).reshape(-1, 1))
            
            yearly_estimates[state] = pd.Series(
                predictions, 
                index=year_range, 
                name='yearly_births'
            )
        
        return pd.DataFrame(yearly_estimates).T
    
    def _estimate_birth_seasonality(self, survey_data: pd.DataFrame) -> pd.Series:
        """
        Estimate seasonal birth patterns using circular statistics.
        
        Paper Reference: Birth seasonality modeling in demographic analysis
        """
        # Convert months to circular coordinates
        survey_data = survey_data.copy()
        survey_data['month_angle'] = 2 * np.pi * (survey_data['birth_month'] - 1) / 12
        
        # Weight by survey weights
        weights = survey_data['mother_weight'].values
        angles = survey_data['month_angle'].values
        
        # Calculate weighted circular mean and concentration
        cos_sum = np.sum(weights * np.cos(angles))
        sin_sum = np.sum(weights * np.sin(angles))
        weight_sum = np.sum(weights)
        
        mean_angle = np.arctan2(sin_sum, cos_sum)
        concentration = np.sqrt(cos_sum**2 + sin_sum**2) / weight_sum
        
        # Generate seasonal multipliers for each month
        months = np.arange(1, 13)
        month_angles = 2 * np.pi * (months - 1) / 12
        
        # Von Mises-like seasonal pattern
        seasonal_multipliers = 1 + concentration * np.cos(month_angles - mean_angle)
        seasonal_multipliers = seasonal_multipliers / seasonal_multipliers.mean()
        
        return pd.Series(seasonal_multipliers, index=months, name='seasonality')
    
    def _combine_yearly_and_seasonal(self, 
                                   yearly_births: pd.DataFrame,
                                   seasonality: pd.Series) -> pd.DataFrame:
        """Combine yearly birth estimates with seasonal patterns."""
        monthly_results = []
        
        for state in yearly_births.index:
            for year in yearly_births.columns:
                yearly_total = yearly_births.loc[state, year]
                
                for month in range(1, 13):
                    seasonal_factor = seasonality[month]
                    monthly_births = (yearly_total * seasonal_factor) / 12
                    
                    monthly_results.append({
                        'state': state,
                        'year': year,
                        'month': month,
                        'births': monthly_births,
                        'date': pd.Timestamp(year=year, month=month, day=15)
                    })
        
        result_df = pd.DataFrame(monthly_results)
        result_df = result_df.set_index('date').sort_index()
        
        return result_df
    
    def process_vaccination_data(self, 
                                survey_data: pd.DataFrame) -> pd.DataFrame:
        """
        Estimate MCV1 coverage rates from survey data.
        
        Uses logistic regression to model vaccination probability as a function
        of child and household characteristics, then post-stratifies to
        population-level estimates.
        
        Args:
            survey_data: Survey data with vaccination records
            
        Returns:
            DataFrame with MCV1 coverage estimates over time
        """
        # Validate vaccination data
        required_cols = ['child_age_months', 'mcv1_received', 'survey_weight', 'state', 'survey_year']
        missing_cols = [col for col in required_cols if col not in survey_data.columns]
        
        if missing_cols:
            raise ValidationError(
                f"Missing required columns in vaccination data: {missing_cols}",
                data_type="vaccination_data",
                suggestions=[
                    "Ensure data includes child_age_months, mcv1_received, survey_weight, state, survey_year",
                    "Check vaccination data processing",
                    "Verify survey data format"
                ]
            )
        
        # Filter to appropriate age range (12-35 months as in paper)
        age_filtered = survey_data[
            (survey_data['child_age_months'] >= 12) & 
            (survey_data['child_age_months'] < 36)
        ].copy()
        
        if len(age_filtered) == 0:
            raise ValidationError(
                "No children in appropriate age range (12-35 months)",
                suggestions=[
                    "Check age variable units (months vs years)",
                    "Verify age filtering logic",
                    "Check survey data completeness"
                ]
            )
        
        # Estimate coverage by state and year
        coverage_estimates = age_filtered.groupby(['state', 'survey_year']).apply(
            lambda x: np.average(x['mcv1_received'], weights=x['survey_weight'])
        ).reset_index(name='mcv1_coverage')
        
        # Interpolate between survey years
        interpolated_coverage = self._interpolate_coverage(coverage_estimates)
        
        return interpolated_coverage
    
    def _interpolate_coverage(self, coverage_data: pd.DataFrame) -> pd.DataFrame:
        """Interpolate coverage estimates between survey years."""
        interpolated_results = []
        
        for state in coverage_data['state'].unique():
            state_data = coverage_data[coverage_data['state'] == state].copy()
            state_data = state_data.sort_values('survey_year')
            
            # Create full year range
            min_year = state_data['survey_year'].min()
            max_year = max(state_data['survey_year'].max(), self.reference_year)
            year_range = range(min_year, max_year + 1)
            
            # Interpolate coverage values
            coverage_interp = np.interp(
                year_range,
                state_data['survey_year'].values,
                state_data['mcv1_coverage'].values
            )
            
            for year, coverage in zip(year_range, coverage_interp):
                interpolated_results.append({
                    'state': state,
                    'year': year,
                    'mcv1_coverage': coverage
                })
        
        return pd.DataFrame(interpolated_results)
    
    def estimate_vaccination_age(self, survey_data: pd.DataFrame) -> Dict[str, float]:
        """
        Estimate typical age at MCV1 vaccination.
        
        Uses kernel density estimation on vaccination ages to find the modal
        age at vaccination, as described in the paper's demographic analysis.
        
        Args:
            survey_data: Survey data with vaccination dates and birth dates
            
        Returns:
            Dictionary with vaccination age statistics
        """
        # Calculate vaccination ages
        survey_data = survey_data.copy()
        survey_data['vaccination_age_months'] = (
            pd.to_datetime(survey_data['vaccination_date']) - 
            pd.to_datetime(survey_data['birth_date'])
        ).dt.days / 30.44  # Convert to months
        
        # Filter to reasonable age range
        valid_ages = survey_data[
            (survey_data['vaccination_age_months'] >= 6) & 
            (survey_data['vaccination_age_months'] <= 24)
        ]['vaccination_age_months'].dropna()
        
        if len(valid_ages) == 0:
            warnings.warn("No valid vaccination ages found")
            return {'modal_age': 9.0, 'mean_age': 9.0, 'std_age': 2.0}
        
        # Estimate modal age using kernel density
        kde = stats.gaussian_kde(valid_ages)
        age_range = np.linspace(6, 24, 1000)
        density = kde(age_range)
        modal_age = age_range[np.argmax(density)]
        
        return {
            'modal_age': modal_age,
            'mean_age': valid_ages.mean(),
            'std_age': valid_ages.std(),
            'n_observations': len(valid_ages)
        }

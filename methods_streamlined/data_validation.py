"""
Data Validation Module

Provides comprehensive validation for input data used in measles transmission modeling.
This module ensures data quality and provides clear error messages to help researchers
identify and fix data issues before running analyses.

References:
- Paper Section: Data validation is implicit throughout the methodology
- Original code: Scattered validation checks across multiple files
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Union
import warnings
from pathlib import Path


class ValidationError(Exception):
    """Custom exception for data validation errors with detailed messages."""
    
    def __init__(self, message: str, data_type: str = None, suggestions: List[str] = None):
        self.data_type = data_type
        self.suggestions = suggestions or []
        
        full_message = f"Data Validation Error: {message}"
        if data_type:
            full_message += f" (Data type: {data_type})"
        if suggestions:
            full_message += f"\n\nSuggestions:\n" + "\n".join(f"- {s}" for s in suggestions)
            
        super().__init__(full_message)


class DataValidator:
    """
    Comprehensive data validator for measles transmission modeling inputs.
    
    This class validates all major data types used in the analysis:
    - Epidemiological time series (cases, births, vaccination coverage)
    - Vaccination campaign data (SIA effects)
    - Survey data (demographic and health surveys)
    - Age distribution data
    
    Each validation method provides detailed error messages and suggestions
    for fixing common data issues.
    """
    
    def __init__(self, strict_mode: bool = True):
        """
        Initialize the validator.
        
        Args:
            strict_mode: If True, raises exceptions for all validation failures.
                        If False, issues warnings for non-critical issues.
        """
        self.strict_mode = strict_mode
        self.validation_log = []
    
    def validate_epidemiological_data(self, df: pd.DataFrame) -> bool:
        """
        Validate epidemiological time series data.
        
        Expected columns:
        - cases: Number of confirmed/probable measles cases
        - births: Number of births (adjusted for routine immunization)
        - mcv1, mcv2: Vaccination coverage rates (0-1)
        - Date index: Semi-monthly or monthly frequency
        
        Args:
            df: DataFrame with epidemiological time series
            
        Returns:
            bool: True if validation passes
            
        Raises:
            ValidationError: If critical validation issues found
        """
        required_cols = ['cases', 'births']
        optional_cols = ['mcv1', 'mcv2', 'mcv1_var', 'mcv2_var', 'births_var']
        
        # Check required columns
        missing_required = [col for col in required_cols if col not in df.columns]
        if missing_required:
            raise ValidationError(
                f"Missing required columns: {missing_required}",
                data_type="epidemiological",
                suggestions=[
                    "Ensure your data has 'cases' and 'births' columns",
                    "Check column names for typos or different naming conventions",
                    "See README.md for expected data format"
                ]
            )
        
        # Check date index
        if not isinstance(df.index, pd.DatetimeIndex):
            raise ValidationError(
                "Data must have a DatetimeIndex",
                data_type="epidemiological",
                suggestions=[
                    "Convert your date column to datetime and set as index",
                    "Use pd.to_datetime() to convert date strings",
                    "Ensure dates are in YYYY-MM-DD format"
                ]
            )
        
        # Check for negative values
        for col in ['cases', 'births']:
            if (df[col] < 0).any():
                raise ValidationError(
                    f"Negative values found in {col} column",
                    data_type="epidemiological",
                    suggestions=[
                        f"Check data source for {col} - negative values not biologically meaningful",
                        "Consider if missing data is coded as negative values",
                        "Replace negative values with NaN or 0 as appropriate"
                    ]
                )
        
        # Check vaccination coverage ranges
        for col in ['mcv1', 'mcv2']:
            if col in df.columns:
                if (df[col] > 1.0).any() or (df[col] < 0.0).any():
                    self._handle_validation_issue(
                        f"Vaccination coverage ({col}) should be between 0 and 1",
                        suggestions=[
                            "If coverage is in percentages, divide by 100",
                            "Check for data entry errors",
                            "Coverage > 100% may indicate administrative issues"
                        ]
                    )
        
        # Check temporal consistency
        time_gaps = df.index.to_series().diff()
        expected_gap = pd.Timedelta(days=15)  # Semi-monthly
        large_gaps = time_gaps > expected_gap * 2
        
        if large_gaps.any():
            self._handle_validation_issue(
                f"Large time gaps detected in data (max: {time_gaps.max()})",
                suggestions=[
                    "Check for missing time periods in your data",
                    "Consider interpolating missing values",
                    "Ensure consistent reporting frequency"
                ]
            )
        
        self.validation_log.append(f"Epidemiological data validation passed: {len(df)} records")
        return True
    
    def validate_sia_data(self, sia_df: pd.DataFrame) -> bool:
        """
        Validate Supplementary Immunization Activity (SIA) data.
        
        Expected format:
        - Rows: Time periods (matching epidemiological data)
        - Columns: Different SIA campaigns
        - Values: Number of doses delivered or coverage rates
        
        Args:
            sia_df: DataFrame with SIA campaign data
            
        Returns:
            bool: True if validation passes
        """
        if sia_df.empty:
            self._handle_validation_issue(
                "SIA data is empty",
                suggestions=[
                    "Check if SIA data file exists and is readable",
                    "Verify SIA campaigns occurred in your study period",
                    "Consider if analysis can proceed without SIA data"
                ]
            )
            return True
        
        # Check for negative values
        if (sia_df < 0).any().any():
            raise ValidationError(
                "Negative values found in SIA data",
                data_type="SIA",
                suggestions=[
                    "SIA doses/coverage cannot be negative",
                    "Check data encoding - missing values may be coded as negative",
                    "Verify data processing pipeline"
                ]
            )
        
        # Check for extremely large values (potential data errors)
        max_reasonable_doses = 1e6  # 1 million doses per campaign
        if (sia_df > max_reasonable_doses).any().any():
            self._handle_validation_issue(
                f"Very large SIA values detected (max: {sia_df.max().max():,.0f})",
                suggestions=[
                    "Check if values are in correct units (doses vs. thousands)",
                    "Verify against official campaign reports",
                    "Consider if values represent cumulative vs. period-specific doses"
                ]
            )
        
        self.validation_log.append(f"SIA data validation passed: {sia_df.shape[1]} campaigns")
        return True
    
    def _handle_validation_issue(self, message: str, suggestions: List[str] = None):
        """Handle validation issues based on strict_mode setting."""
        if self.strict_mode:
            raise ValidationError(message, suggestions=suggestions)
        else:
            warnings.warn(f"Validation Warning: {message}")
            self.validation_log.append(f"WARNING: {message}")
    
    def validate_survey_data(self, survey_df: pd.DataFrame, survey_type: str = "DHS") -> bool:
        """
        Validate demographic and health survey data.

        Args:
            survey_df: Survey data with demographic variables
            survey_type: Type of survey ("DHS" or "MICS")

        Returns:
            bool: True if validation passes
        """
        expected_cols = ['age', 'vaccination_status', 'weight']

        missing_cols = [col for col in expected_cols if col not in survey_df.columns]
        if missing_cols:
            self._handle_validation_issue(
                f"Missing expected survey columns: {missing_cols}",
                suggestions=[
                    f"Check {survey_type} data processing pipeline",
                    "Verify column naming conventions",
                    "See demographic_analysis.py for expected format"
                ]
            )

        # Check age ranges
        if 'age' in survey_df.columns:
            age_range = (survey_df['age'].min(), survey_df['age'].max())
            if age_range[0] < 0 or age_range[1] > 120:
                self._handle_validation_issue(
                    f"Unusual age range detected: {age_range}",
                    suggestions=[
                        "Check age units (months vs. years)",
                        "Verify data cleaning procedures",
                        "Consider age-specific analysis requirements"
                    ]
                )

        self.validation_log.append(f"{survey_type} survey validation passed: {len(survey_df)} records")
        return True

    def get_validation_summary(self) -> str:
        """Return a summary of all validation checks performed."""
        if not self.validation_log:
            return "No validation checks performed yet."

        return "\n".join([
            "=== Data Validation Summary ===",
            *self.validation_log,
            f"Total checks: {len(self.validation_log)}"
        ])

"""
Case Classification Module

Implements logistic regression for classifying untested, clinically compatible cases
as probable measles cases. This addresses the challenge that not all suspected cases
can be laboratory tested, requiring statistical methods to estimate the probability
that clinical symptoms represent true measles infection.

Paper Reference:
- Main methodology: Appendix 1 "Untested, isolated, clinically compatible cases"
- Figure: Appendix 1, Figure 1 (incidence curves and confirmation probability)
- Mathematical model: Equation in Appendix 1

Original code: methods/epi_curves/logistic.py

The classification model uses individual-level covariates:
- Age of the patient
- Number of reported vaccine doses
- Temporal patterns in laboratory confirmation rates
- Clinical presentation details

This is critical for accurate burden estimation since surveillance systems
often have incomplete laboratory testing, especially during outbreaks.
"""

import numpy as np
import pandas as pd
from scipy.optimize import minimize
from scipy.linalg import block_diag
from typing import Dict, List, Optional, Tuple, Union
import warnings

# Handle imports for both module and script execution
try:
    from .data_validation import DataValidator, ValidationError
except ImportError:
    from data_validation import DataValidator, ValidationError


def logistic_function(x: np.ndarray) -> np.ndarray:
    """
    Standard logistic (sigmoid) function.
    
    Args:
        x: Input array
        
    Returns:
        Logistic transformation of input
    """
    # Clip extreme values to prevent overflow
    x_clipped = np.clip(x, -500, 500)
    return 1.0 / (1.0 + np.exp(-x_clipped))


class CaseClassifier:
    """
    Logistic regression classifier for measles case confirmation.
    
    This class implements the statistical model described in Appendix 1 of the paper
    for estimating the probability that untested, clinically compatible cases are
    actually measles. The model accounts for:
    
    - Individual characteristics (age, vaccination history)
    - Temporal variation in confirmation rates
    - Laboratory testing patterns and capacity
    
    Paper Reference: Appendix 1, Equation for p(IgM+|a,d,t)
    Original code: LogisticRegressionPosterior class in epi_curves/logistic.py
    """
    
    def __init__(self, regularization_strength: float = 1.0):
        """
        Initialize the case classifier.
        
        Args:
            regularization_strength: L2 regularization parameter to prevent overfitting
        """
        self.regularization_strength = regularization_strength
        self.validator = DataValidator()
        self.is_fitted = False
        self.coefficients = None
        self.feature_names = None
    
    def prepare_features(self, 
                        case_data: pd.DataFrame,
                        include_temporal: bool = True) -> Tuple[np.ndarray, List[str]]:
        """
        Prepare feature matrix for logistic regression.
        
        Creates indicator variables for:
        - Age groups (≤5 years vs >5 years, as in paper)
        - Vaccination status (0, 1, 2+, or missing doses)
        - Temporal effects (if requested)
        
        Args:
            case_data: DataFrame with case information
            include_temporal: Whether to include time-varying effects
            
        Returns:
            Tuple of (feature_matrix, feature_names)
            
        Raises:
            ValidationError: If required columns are missing
        """
        required_cols = ['age', 'vaccine_doses', 'lab_result']
        missing_cols = [col for col in required_cols if col not in case_data.columns]
        
        if missing_cols:
            raise ValidationError(
                f"Missing required columns for case classification: {missing_cols}",
                data_type="case_data",
                suggestions=[
                    "Ensure case data includes age, vaccine_doses, and lab_result columns",
                    "Check column naming conventions",
                    "See case_classification.py documentation for expected format"
                ]
            )
        
        features = []
        feature_names = []
        
        # Intercept term
        features.append(np.ones(len(case_data)))
        feature_names.append('intercept')
        
        # Age indicator (>5 years old, as in paper's Appendix 1)
        age_over_5 = (case_data['age'] > 5).astype(int)
        features.append(age_over_5)
        feature_names.append('age_over_5')
        
        # Vaccination dose indicators
        # Following paper's categorization: 0, 1, 2+, missing
        dose_1 = (case_data['vaccine_doses'] == 1).astype(int)
        dose_2_plus = (case_data['vaccine_doses'] >= 2).astype(int)
        dose_missing = case_data['vaccine_doses'].isna().astype(int)
        
        features.extend([dose_1, dose_2_plus, dose_missing])
        feature_names.extend(['dose_1', 'dose_2_plus', 'dose_missing'])
        
        # Temporal effects (if requested)
        if include_temporal and 'date' in case_data.columns:
            # Create time periods (e.g., 2-month windows as in paper)
            case_data = case_data.copy()
            case_data['time_period'] = pd.to_datetime(case_data['date']).dt.to_period('2M')
            
            # Create dummy variables for each time period
            time_dummies = pd.get_dummies(case_data['time_period'], prefix='time')
            
            for col in time_dummies.columns:
                features.append(time_dummies[col].values)
                feature_names.append(col)
        
        # Combine all features
        X = np.column_stack(features)
        
        return X, feature_names
    
    def fit(self, 
            case_data: pd.DataFrame,
            include_temporal: bool = True,
            max_iterations: int = 1000) -> Dict[str, Union[float, bool]]:
        """
        Fit the logistic regression model to case data.
        
        Args:
            case_data: DataFrame with case information and lab results
            include_temporal: Whether to include temporal effects
            max_iterations: Maximum optimization iterations
            
        Returns:
            Dictionary with fitting results and diagnostics
            
        Raises:
            ValidationError: If data validation fails
            RuntimeError: If optimization fails to converge
        """
        # Prepare features and target
        X, feature_names = self.prepare_features(case_data, include_temporal)
        
        # Target variable: 1 if lab confirmed, 0 if lab rejected
        # Exclude untested cases from training
        tested_mask = case_data['lab_result'].notna()
        X_train = X[tested_mask]
        y_train = (case_data.loc[tested_mask, 'lab_result'] == 'confirmed').astype(int)
        
        if len(y_train) == 0:
            raise ValidationError(
                "No tested cases available for training",
                suggestions=[
                    "Ensure lab_result column contains 'confirmed' and 'rejected' values",
                    "Check that some cases have laboratory test results",
                    "Verify data processing pipeline"
                ]
            )
        
        # Set up regularization matrix
        n_features = X_train.shape[1]
        regularization_matrix = self.regularization_strength * np.eye(n_features)
        # Don't regularize intercept
        regularization_matrix[0, 0] = 0
        
        # Define objective function (negative log-likelihood + penalty)
        def objective(beta):
            linear_pred = X_train @ beta
            prob_pred = logistic_function(linear_pred)
            
            # Avoid log(0) by adding small epsilon
            epsilon = 1e-15
            prob_pred = np.clip(prob_pred, epsilon, 1 - epsilon)
            
            # Log-likelihood
            log_likelihood = np.sum(
                y_train * np.log(prob_pred) + (1 - y_train) * np.log(1 - prob_pred)
            )
            
            # L2 penalty
            penalty = 0.5 * beta.T @ regularization_matrix @ beta
            
            return -(log_likelihood - penalty)
        
        # Define gradient
        def gradient(beta):
            linear_pred = X_train @ beta
            prob_pred = logistic_function(linear_pred)
            
            # Gradient of log-likelihood
            residuals = y_train - prob_pred
            grad_likelihood = X_train.T @ residuals
            
            # Gradient of penalty
            grad_penalty = regularization_matrix @ beta
            
            return -(grad_likelihood - grad_penalty)
        
        # Optimize
        try:
            result = minimize(
                objective,
                x0=np.zeros(n_features),
                method='BFGS',
                jac=gradient,
                options={'maxiter': max_iterations}
            )
            
            if not result.success:
                warnings.warn(f"Optimization did not converge: {result.message}")
            
            # Store results
            self.coefficients = result.x
            self.feature_names = feature_names
            self.is_fitted = True
            
            # Compute diagnostics
            final_likelihood = -objective(result.x)
            
            return {
                'converged': result.success,
                'log_likelihood': final_likelihood,
                'n_iterations': result.nit,
                'n_training_cases': len(y_train),
                'confirmation_rate': y_train.mean()
            }
            
        except Exception as e:
            raise RuntimeError(f"Model fitting failed: {str(e)}") from e
    
    def predict_probability(self, case_data: pd.DataFrame) -> np.ndarray:
        """
        Predict confirmation probability for cases.
        
        Args:
            case_data: DataFrame with case information
            
        Returns:
            Array of confirmation probabilities
            
        Raises:
            RuntimeError: If model has not been fitted
        """
        if not self.is_fitted:
            raise RuntimeError("Model must be fitted before making predictions")
        
        # Prepare features (using same temporal setting as training)
        X, _ = self.prepare_features(case_data, include_temporal=True)
        
        # Make predictions
        linear_pred = X @ self.coefficients
        probabilities = logistic_function(linear_pred)
        
        return probabilities
    
    def classify_untested_cases(self, 
                               case_data: pd.DataFrame,
                               threshold: float = 0.9) -> pd.DataFrame:
        """
        Classify untested cases as probable measles.
        
        Following the paper's approach, cases with >90% confirmation probability
        are considered probable measles cases.
        
        Args:
            case_data: DataFrame with all cases (tested and untested)
            threshold: Probability threshold for classification (default: 0.9)
            
        Returns:
            DataFrame with classification results
        """
        if not self.is_fitted:
            raise RuntimeError("Model must be fitted before classification")
        
        result_df = case_data.copy()
        
        # Predict probabilities for all cases
        probabilities = self.predict_probability(case_data)
        result_df['confirmation_probability'] = probabilities
        
        # Classify cases
        result_df['final_classification'] = 'untested'
        
        # Lab confirmed cases
        confirmed_mask = result_df['lab_result'] == 'confirmed'
        result_df.loc[confirmed_mask, 'final_classification'] = 'confirmed'
        
        # Lab rejected cases  
        rejected_mask = result_df['lab_result'] == 'rejected'
        result_df.loc[rejected_mask, 'final_classification'] = 'rejected'
        
        # High-probability untested cases
        untested_mask = result_df['lab_result'].isna()
        high_prob_mask = untested_mask & (probabilities >= threshold)
        result_df.loc[high_prob_mask, 'final_classification'] = 'probable'
        
        return result_df

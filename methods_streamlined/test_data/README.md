# Example Data for Measles Transmission Modeling

This directory contains synthetic example data demonstrating the expected format
for the streamlined measles transmission modeling methods.

## Files:

- `epidemiological_data.csv`: Time series of cases, births, and vaccination coverage
- `sia_data.csv`: Supplementary immunization activity (campaign) data  
- `case_data.csv`: Individual case records for classification analysis
- `survey_data.csv`: Household survey data for demographic analysis

## Usage:

```bash
# Run analysis on this example data
python ../main.py . --output ./results
```

## Note:

This is synthetic data for demonstration purposes only. Real analysis should
use actual surveillance and survey data following the same format.

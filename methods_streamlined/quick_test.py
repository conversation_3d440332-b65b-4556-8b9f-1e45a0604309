#!/usr/bin/env python3
"""
Quick Test Script

This script runs a simplified version of the analysis pipeline to verify
that the core functionality works without running the computationally
intensive age distribution analysis.
"""

import sys
import pandas as pd
import numpy as np
from pathlib import Path

# Import the modules
try:
    from transmission_model import MeaslesTransmissionModel, ModelInputProcessor, ModelParameters
    from case_classification import CaseClassifier
    from data_validation import DataValidator
except ImportError as e:
    print(f"Import error: {e}")
    sys.exit(1)


def quick_test():
    """Run a quick test of the core functionality."""
    print("=" * 60)
    print("QUICK TEST - Streamlined Measles Transmission Modeling")
    print("=" * 60)
    
    # Test 1: Data Validation
    print("\n1. Testing Data Validation...")
    validator = DataValidator()
    
    # Create simple test data
    dates = pd.date_range('2020-01-01', '2020-06-30', freq='SMS')  # Semi-monthly
    epi_data = pd.DataFrame({
        'cases': np.random.poisson(20, len(dates)),
        'births': np.random.poisson(1000, len(dates)),
        'mcv1': np.random.uniform(0.7, 0.9, len(dates)),
        'mcv2': np.random.uniform(0.5, 0.7, len(dates))
    }, index=dates)
    
    try:
        validator.validate_epidemiological_data(epi_data)
        print("✓ Epidemiological data validation passed")
    except Exception as e:
        print(f"✗ Validation failed: {e}")
        return False
    
    # Test 2: Model Parameters
    print("\n2. Testing Model Parameters...")
    try:
        params = ModelParameters(
            mcv1_efficacy=0.825,
            mcv2_efficacy=0.95,
            sia_efficacy_guess=0.1
        )
        print("✓ Model parameters created successfully")
    except Exception as e:
        print(f"✗ Parameter creation failed: {e}")
        return False
    
    # Test 3: Input Processing
    print("\n3. Testing Input Processing...")
    try:
        processor = ModelInputProcessor(parameters=params)
        processed_data = processor.process_epidemiological_data(epi_data)
        print(f"✓ Data processing successful: {len(processed_data)} records")
    except Exception as e:
        print(f"✗ Data processing failed: {e}")
        return False
    
    # Test 4: Case Classification (with synthetic data)
    print("\n4. Testing Case Classification...")
    try:
        # Create synthetic case data
        n_cases = 100
        case_data = pd.DataFrame({
            'age': np.random.exponential(3, n_cases) + 0.5,
            'vaccine_doses': np.random.choice([0, 1, 2], n_cases, p=[0.2, 0.4, 0.4]),
            'lab_result': np.random.choice(['confirmed', 'rejected', None], n_cases, p=[0.3, 0.4, 0.3]),
            'date': pd.date_range('2020-01-01', periods=n_cases, freq='D')
        })
        
        classifier = CaseClassifier()
        
        # Only fit if we have some tested cases
        tested_cases = case_data.dropna(subset=['lab_result'])
        if len(tested_cases) > 10:
            fit_results = classifier.fit(case_data)
            print(f"✓ Case classification fitted: {fit_results['converged']}")
            
            # Classify cases
            classified = classifier.classify_untested_cases(case_data)
            print(f"✓ Cases classified: {len(classified)} total cases")
        else:
            print("✓ Case classification skipped (insufficient test data)")
            
    except Exception as e:
        print(f"✗ Case classification failed: {e}")
        return False
    
    # Test 5: Transmission Model (simplified)
    print("\n5. Testing Transmission Model...")
    try:
        model = MeaslesTransmissionModel(parameters=params)
        
        # Create simple SIA data
        sia_data = pd.DataFrame(
            0, 
            index=processed_data.index,
            columns=['campaign_2020']
        )
        # Add some campaign doses
        sia_data.iloc[10:12, 0] = 50000  # Campaign in middle of period
        
        print("✓ Transmission model initialized")
        print(f"✓ SIA data prepared: {sia_data.sum().sum()} total doses")
        
        # Note: We're not actually fitting the model here as it's computationally intensive
        # In a real scenario, you would call:
        # results = model.fit(processed_data, sia_data, initial_susceptibility=5000, susceptibility_variance=1000)
        print("✓ Model ready for fitting (skipping actual fit for quick test)")
        
    except Exception as e:
        print(f"✗ Transmission model test failed: {e}")
        return False
    
    print("\n" + "=" * 60)
    print("🎉 ALL QUICK TESTS PASSED!")
    print("=" * 60)
    print("\nThe streamlined methods package is working correctly.")
    print("\nNext steps:")
    print("1. Generate example data: python3 generate_example_data.py --output ./example_data")
    print("2. Run full analysis: python3 main.py ./example_data --output ./results")
    print("3. Or use individual components in your own scripts")
    
    return True


if __name__ == "__main__":
    success = quick_test()
    sys.exit(0 if success else 1)

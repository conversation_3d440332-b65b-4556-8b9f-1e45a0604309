#!/usr/bin/env python3
"""
Installation Test Script

This script tests that the streamlined methods package is properly installed
and can import all components without errors.

Usage:
    python test_installation.py
"""

import sys
import traceback
from pathlib import Path


def test_imports():
    """Test that all main components can be imported."""
    print("Testing package imports...")
    
    try:
        # Test main package import (try both package and direct imports)
        try:
            import methods_streamlined
            print("✓ Main package imported successfully")

            # Test individual components
            from methods_streamlined import (
                MeaslesTransmissionModel,
                ModelInputProcessor,
                CaseClassifier,
                AgeDistributionEstimator,
                DemographicProcessor,
                DataValidator,
                ValidationError,
                ModelParameters
            )
        except ImportError:
            # Try direct imports (when running from within the directory)
            from transmission_model import MeaslesTransmissionModel, ModelInputProcessor, ModelParameters
            from case_classification import CaseClassifier
            from age_distribution import AgeDistributionEstimator
            from demographic_analysis import DemographicProcessor
            from data_validation import DataValidator, ValidationError
            print("✓ Direct imports successful (running from source directory)")

        print("✓ All main classes imported successfully")
        
        # Test that classes can be instantiated
        validator = DataValidator()
        params = ModelParameters()
        model = MeaslesTransmissionModel(params)
        processor = ModelInputProcessor(params)
        classifier = CaseClassifier()
        age_estimator = AgeDistributionEstimator()
        demo_processor = DemographicProcessor()
        
        print("✓ All classes can be instantiated")
        
        return True
        
    except Exception as e:
        print(f"✗ Import test failed: {str(e)}")
        traceback.print_exc()
        return False


def test_dependencies():
    """Test that all required dependencies are available."""
    print("\nTesting dependencies...")
    
    required_packages = [
        'numpy',
        'pandas', 
        'scipy',
        'sklearn',
        'matplotlib'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✓ {package} available")
        except ImportError:
            print(f"✗ {package} missing")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\nMissing packages: {missing_packages}")
        print("Install with: pip install " + " ".join(missing_packages))
        return False
    
    return True


def test_data_validation():
    """Test basic data validation functionality."""
    print("\nTesting data validation...")
    
    try:
        import pandas as pd
        import numpy as np

        # Try both import methods
        try:
            from methods_streamlined import DataValidator, ValidationError
        except ImportError:
            from data_validation import DataValidator, ValidationError
        
        # Create test data
        dates = pd.date_range('2020-01-01', '2020-12-31', freq='M')
        test_data = pd.DataFrame({
            'cases': np.random.poisson(20, len(dates)),
            'births': np.random.poisson(1000, len(dates)),
            'mcv1': np.random.uniform(0.7, 0.9, len(dates)),
            'mcv2': np.random.uniform(0.5, 0.7, len(dates))
        }, index=dates)
        
        # Test validation
        validator = DataValidator(strict_mode=False)
        result = validator.validate_epidemiological_data(test_data)
        
        if result:
            print("✓ Data validation working correctly")
            return True
        else:
            print("✗ Data validation failed")
            return False
            
    except Exception as e:
        print(f"✗ Data validation test failed: {str(e)}")
        return False


def test_model_parameters():
    """Test model parameter validation."""
    print("\nTesting model parameters...")
    
    try:
        # Try both import methods
        try:
            from methods_streamlined import ModelParameters
        except ImportError:
            from transmission_model import ModelParameters
        
        # Test valid parameters
        params = ModelParameters(
            mcv1_efficacy=0.825,
            mcv2_efficacy=0.95,
            sia_efficacy_guess=0.1
        )
        print("✓ Valid parameters accepted")
        
        # Test invalid parameters
        try:
            invalid_params = ModelParameters(mcv1_efficacy=1.5)  # > 1.0
            print("✗ Invalid parameters should have been rejected")
            return False
        except ValueError:
            print("✓ Invalid parameters correctly rejected")
        
        return True
        
    except Exception as e:
        print(f"✗ Parameter validation test failed: {str(e)}")
        return False


def main():
    """Run all installation tests."""
    print("=" * 60)
    print("STREAMLINED MEASLES TRANSMISSION MODELING")
    print("Installation Test Suite")
    print("=" * 60)
    
    tests = [
        ("Package Imports", test_imports),
        ("Dependencies", test_dependencies),
        ("Data Validation", test_data_validation),
        ("Model Parameters", test_model_parameters)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        print("-" * 40)
        success = test_func()
        results.append((test_name, success))
    
    # Summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    
    all_passed = True
    for test_name, success in results:
        status = "PASS" if success else "FAIL"
        print(f"{test_name:.<40} {status}")
        if not success:
            all_passed = False
    
    print("-" * 60)
    
    if all_passed:
        print("🎉 ALL TESTS PASSED!")
        print("\nThe package is ready to use. Try:")
        print("  python generate_example_data.py --output ./example_data")
        print("  python main.py ./example_data --output ./results")
        return 0
    else:
        print("❌ SOME TESTS FAILED!")
        print("\nPlease check the error messages above and:")
        print("1. Install missing dependencies")
        print("2. Verify Python version >= 3.10")
        print("3. Check package installation")
        return 1


if __name__ == "__main__":
    sys.exit(main())

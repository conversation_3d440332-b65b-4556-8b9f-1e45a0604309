"""
Age Distribution Estimation Module

Implements Bayesian smoothing methods for estimating age-at-infection distributions
by birth cohort. This addresses the challenge of sparse, noisy age data in surveillance
systems by borrowing strength across cohorts and ages.

Paper Reference:
- Main methodology: Appendix 2, age distribution modeling
- Application: Used to estimate age patterns referenced in manuscript's second appendix
- Data source: Age-at-symptom onset data from surveillance (Figure 1, red steps)

Original code: methods/age_at_inf/StratifiedDistribution.py and masked_buckets.py

The method uses:
1. Binomial likelihood for age-specific case counts
2. Smoothing priors across ages and birth cohorts
3. Masking for periods with insufficient observation time
4. Regional stratification to account for geographic variation

This is essential for understanding transmission patterns and validating model
assumptions about age-specific infection risks.
"""

import numpy as np
import pandas as pd
from scipy.optimize import minimize
from scipy.sparse import diags
from typing import Dict, List, Optional, Tuple, Union
import warnings

# Handle imports for both module and script execution
try:
    from .data_validation import DataValidator, ValidationError
except ImportError:
    from data_validation import DataValidator, ValidationError


class AgeDistributionEstimator:
    """
    Bayesian estimator for age-at-infection distributions by birth cohort.
    
    This class implements the smoothing methodology described in the paper's
    second appendix for estimating how infection risk varies by age and birth year.
    The approach uses Bayesian methods to smooth sparse surveillance data while
    accounting for observation windows and demographic changes.
    
    Key features:
    - Binomial likelihood for age-specific case counts
    - Second-order random walk priors for smoothing
    - Masking for incomplete observation periods
    - Regional stratification capabilities
    
    Paper Reference: Appendix 2, age distribution smoothing
    Original code: BinomialPosterior class in age_at_inf/masked_buckets.py
    """
    
    def __init__(self, 
                 correlation_time: float = 10.0,
                 age_correlation: float = 4.0,
                 max_age: int = 25):
        """
        Initialize the age distribution estimator.
        
        Args:
            correlation_time: Correlation length across birth years
            age_correlation: Correlation length across ages  
            max_age: Maximum age to consider (years)
        """
        self.correlation_time = correlation_time
        self.age_correlation = age_correlation
        self.max_age = max_age
        self.validator = DataValidator()
        self.is_fitted = False
        
    def prepare_age_data(self, 
                        case_data: pd.DataFrame,
                        min_birth_year: int = 2002,
                        max_birth_year: Optional[int] = None) -> Tuple[pd.DataFrame, np.ndarray]:
        """
        Prepare age-at-infection data for analysis.
        
        Args:
            case_data: DataFrame with columns ['date', 'age', 'birth_year']
            min_birth_year: Earliest birth year to include
            max_birth_year: Latest birth year to include (default: current year)
            
        Returns:
            Tuple of (age_count_matrix, observation_mask)
            
        Raises:
            ValidationError: If required columns are missing
        """
        required_cols = ['date', 'age', 'birth_year']
        missing_cols = [col for col in required_cols if col not in case_data.columns]
        
        if missing_cols:
            raise ValidationError(
                f"Missing required columns for age analysis: {missing_cols}",
                data_type="age_data",
                suggestions=[
                    "Ensure case data includes date, age, and birth_year columns",
                    "Calculate birth_year from date and age if not available",
                    "Check data preprocessing pipeline"
                ]
            )
        
        # Set default max birth year
        if max_birth_year is None:
            max_birth_year = pd.to_datetime(case_data['date']).dt.year.max()
        
        # Filter data to specified birth year range
        data_filtered = case_data[
            (case_data['birth_year'] >= min_birth_year) & 
            (case_data['birth_year'] <= max_birth_year) &
            (case_data['age'] <= self.max_age)
        ].copy()
        
        if len(data_filtered) == 0:
            raise ValidationError(
                f"No cases found in birth year range {min_birth_year}-{max_birth_year}",
                suggestions=[
                    "Check birth year calculation",
                    "Verify date range covers expected period",
                    "Consider expanding birth year range"
                ]
            )
        
        # Create age count matrix (birth_year x age)
        age_counts = data_filtered.groupby(['birth_year', 'age']).size().unstack(fill_value=0)
        
        # Ensure all birth years and ages are represented
        birth_years = range(min_birth_year, max_birth_year + 1)
        ages = range(0, self.max_age + 1)
        
        age_counts = age_counts.reindex(
            index=birth_years, 
            columns=ages, 
            fill_value=0
        )
        
        # Create observation mask
        # Mask periods where cohorts couldn't be fully observed
        observation_mask = self._create_observation_mask(
            birth_years, ages, case_data['date'].min(), case_data['date'].max()
        )
        
        return age_counts, observation_mask
    
    def _create_observation_mask(self, 
                                birth_years: range,
                                ages: range, 
                                data_start: str,
                                data_end: str) -> np.ndarray:
        """
        Create mask indicating which (birth_year, age) combinations are observable.
        
        A combination is observable if the cohort had sufficient time to reach
        that age within the observation window.
        
        Args:
            birth_years: Range of birth years
            ages: Range of ages
            data_start: Start of observation period
            data_end: End of observation period
            
        Returns:
            Binary mask array (birth_year x age)
        """
        data_start_year = pd.to_datetime(data_start).year
        data_end_year = pd.to_datetime(data_end).year
        
        mask = np.zeros((len(birth_years), len(ages)))
        
        for i, birth_year in enumerate(birth_years):
            for j, age in enumerate(ages):
                # Year when cohort reaches this age
                age_year = birth_year + age
                
                # Observable if within data collection period
                if data_start_year <= age_year <= data_end_year:
                    mask[i, j] = 1
        
        return mask
    
    def fit(self, 
            age_counts: pd.DataFrame,
            observation_mask: np.ndarray,
            max_iterations: int = 1000) -> Dict[str, Union[float, bool, np.ndarray]]:
        """
        Fit the Bayesian age distribution model.
        
        Args:
            age_counts: Matrix of case counts (birth_year x age)
            observation_mask: Binary mask for observable combinations
            max_iterations: Maximum optimization iterations
            
        Returns:
            Dictionary with fitting results and estimated distributions
        """
        self.age_counts = age_counts.values
        self.observation_mask = observation_mask
        self.birth_years = age_counts.index.values
        self.ages = age_counts.columns.values
        
        # Set up smoothing matrices
        self._setup_smoothing_matrices()
        
        # Initialize parameters
        n_birth_years, n_ages = self.age_counts.shape
        initial_params = np.zeros(n_birth_years * n_ages)
        
        # Define objective function
        def objective(params):
            return -self._log_posterior(params)
        
        def gradient(params):
            return -self._log_posterior_gradient(params)
        
        try:
            # Optimize
            result = minimize(
                objective,
                x0=initial_params,
                method='BFGS',
                jac=gradient,
                options={'maxiter': max_iterations}
            )
            
            if not result.success:
                warnings.warn(f"Optimization did not converge: {result.message}")
            
            # Store results
            self.fitted_params = result.x.reshape(n_birth_years, n_ages)
            self.is_fitted = True
            
            # Convert to probabilities
            self.age_probabilities = self._params_to_probabilities(result.x)
            
            return {
                'converged': result.success,
                'log_posterior': -result.fun,
                'n_iterations': result.nit,
                'age_probabilities': self.age_probabilities
            }
            
        except Exception as e:
            raise RuntimeError(f"Model fitting failed: {str(e)}") from e
    
    def _setup_smoothing_matrices(self):
        """Set up precision matrices for smoothing priors."""
        n_birth_years, n_ages = self.age_counts.shape
        
        # Smoothing across birth years (temporal correlation)
        D_time = np.diff(np.eye(n_birth_years), n=2, axis=0)
        self.precision_time = (D_time.T @ D_time) / (self.correlation_time ** 2)
        
        # Smoothing across ages
        D_age = np.diff(np.eye(n_ages), n=2, axis=0)
        self.precision_age = (D_age.T @ D_age) / (self.age_correlation ** 2)
        
        # Combined precision matrix (Kronecker product)
        self.precision_matrix = np.kron(np.eye(n_birth_years), self.precision_age) + \
                               np.kron(self.precision_time, np.eye(n_ages))
    
    def _log_posterior(self, params: np.ndarray) -> float:
        """
        Evaluate log posterior density.
        
        Combines binomial likelihood with smoothing priors.
        """
        n_birth_years, n_ages = self.age_counts.shape
        params_matrix = params.reshape(n_birth_years, n_ages)
        
        # Convert to probabilities
        probs = self._params_to_probabilities(params)
        probs_matrix = probs.reshape(n_birth_years, n_ages)
        
        # Binomial log-likelihood (only for observable combinations)
        log_likelihood = 0
        for i in range(n_birth_years):
            for j in range(n_ages):
                if self.observation_mask[i, j] > 0:
                    count = self.age_counts[i, j]
                    total = self.age_counts[i, :].sum()
                    if total > 0:
                        prob = probs_matrix[i, j]
                        prob = np.clip(prob, 1e-15, 1 - 1e-15)  # Avoid log(0)
                        log_likelihood += count * np.log(prob)
        
        # Smoothing prior
        log_prior = -0.5 * params.T @ self.precision_matrix @ params
        
        return log_likelihood + log_prior
    
    def _log_posterior_gradient(self, params: np.ndarray) -> np.ndarray:
        """Compute gradient of log posterior."""
        # Simplified gradient computation
        # Full implementation would include exact derivatives
        epsilon = 1e-8
        grad = np.zeros_like(params)
        
        for i in range(len(params)):
            params_plus = params.copy()
            params_plus[i] += epsilon
            params_minus = params.copy()
            params_minus[i] -= epsilon
            
            grad[i] = (self._log_posterior(params_plus) - 
                      self._log_posterior(params_minus)) / (2 * epsilon)
        
        return grad
    
    def _params_to_probabilities(self, params: np.ndarray) -> np.ndarray:
        """Convert unconstrained parameters to probabilities using softmax."""
        n_birth_years, n_ages = self.age_counts.shape
        params_matrix = params.reshape(n_birth_years, n_ages)
        
        # Apply softmax across ages for each birth year
        probs = np.zeros_like(params_matrix)
        for i in range(n_birth_years):
            exp_params = np.exp(params_matrix[i, :] - np.max(params_matrix[i, :]))
            probs[i, :] = exp_params / np.sum(exp_params)
        
        return probs.flatten()
    
    def predict_age_distribution(self, birth_year: int) -> pd.Series:
        """
        Predict age-at-infection distribution for a specific birth cohort.
        
        Args:
            birth_year: Birth year of interest
            
        Returns:
            Series with age-specific infection probabilities
        """
        if not self.is_fitted:
            raise RuntimeError("Model must be fitted before making predictions")
        
        if birth_year not in self.birth_years:
            raise ValueError(f"Birth year {birth_year} not in fitted data")
        
        birth_year_idx = np.where(self.birth_years == birth_year)[0][0]
        age_probs = self.age_probabilities.reshape(len(self.birth_years), len(self.ages))
        
        return pd.Series(
            age_probs[birth_year_idx, :],
            index=self.ages,
            name=f'age_distribution_{birth_year}'
        )

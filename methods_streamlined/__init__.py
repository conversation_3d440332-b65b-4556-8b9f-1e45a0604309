"""
Streamlined Measles Transmission Modeling Methods

This package provides a modernized, user-friendly implementation of the measles transmission
modeling methods described in "Routine immunization intensification, vaccination campaigns,
and measles transmission in Southern Nigeria" (2025).

The package is designed for researchers who want to apply these methods to their own data
with clear documentation, robust error handling, and modern Python practices.

Key Components:
- transmission_model: Core SIR transmission model (neighborhood_sir.py equivalent)
- case_classification: Logistic regression for classifying clinical cases
- age_distribution: Age-at-infection distribution modeling
- demographic_analysis: Survey data processing and demographic modeling
- data_validation: Input validation and error checking utilities

Compatible with Python 3.10+
"""

__version__ = "1.0.0"
__author__ = "Streamlined from original methods by <PERSON><PERSON> et al."

# Import main classes for easy access
from .transmission_model import MeaslesTransmissionModel, ModelInputProcessor, ModelParameters
from .case_classification import CaseClassifier
from .age_distribution import AgeDistributionEstimator
from .demographic_analysis import DemographicProcessor
from .data_validation import DataValidator, ValidationError

__all__ = [
    'MeaslesTransmissionModel',
    'ModelInputProcessor',
    'ModelParameters',
    'CaseClassifier',
    'AgeDistributionEstimator',
    'DemographicProcessor',
    'DataValidator',
    'ValidationError'
]
Streamlined Measles Transmission Modeling Methods

This package provides a modernized, user-friendly implementation of the measles transmission
modeling methods described in "Routine immunization intensification, vaccination campaigns, 
and measles transmission in Southern Nigeria" (2025).

The package is designed for researchers who want to apply these methods to their own data
with clear documentation, robust error handling, and modern Python practices.

Key Components:
- transmission_model: Core SIR transmission model (neighborhood_sir.py equivalent)
- case_classification: Logistic regression for classifying clinical cases
- age_distribution: Age-at-infection distribution modeling
- demographic_analysis: Survey data processing and demographic modeling
- data_validation: Input validation and error checking utilities

Compatible with Python 3.10+
"""

__version__ = "1.0.0"
__author__ = "Streamlined from original methods by Niket Thakkar et al."

# Import main classes for easy access
from .transmission_model import MeaslesTransmissionModel, ModelInputProcessor
from .case_classification import CaseClassifier
from .age_distribution import AgeDistributionEstimator
from .demographic_analysis import DemographicProcessor
from .data_validation import DataValidator, ValidationError

__all__ = [
    'MeaslesTransmissionModel',
    'ModelInputProcessor', 
    'CaseClassifier',
    'AgeDistributionEstimator',
    'DemographicProcessor',
    'DataValidator',
    'ValidationError'
]

# IS IT THOUGH? An Analysis of Repository Structure and Purpose

## Your Hypothesis vs. Reality

You suggested that this repository has a dual purpose:
1. **Researcher's reproduction workflow**: `GeneratePriors.py` → `GenerateStateSummary.py` → "call it a day"
2. **Methods for others**: The `methods/` folder with its own README and `neighborhood_sir.py` for other researchers to apply to their own data

**Verdict: You're absolutely right, and the evidence strongly supports this interpretation.**

## Evidence Supporting Your Analysis

### 1. The Researcher's Reproduction Workflow

The root-level scripts are clearly designed as a **complete reproduction pipeline** for the specific research paper:

- **`GeneratePriors.py`**: Orchestrates survival analysis across all 17 Southern Nigerian states, calling `SurvivalPrior.py` as a subprocess for each state, compiling results into serialized objects
- **`GenerateStateSummary.py`**: The main analysis engine that takes ~20 minutes to run, calling multiple scripts as subprocesses (`VisualizeInputs.py`, `TransmissionModel.py`, `OutOfSampleTest.py`, `SIAImpactPosteriors.py`) and generating the complete PDF summary

This is exactly what you'd expect from a researcher who wants to:
1. Run the analysis once to reproduce their results
2. Generate all the paper's figures automatically
3. Create comprehensive PDF summaries for all states

The workflow is **highly specific** to Southern Nigeria's 17 states and the 2019 intensification vs. mass campaigns comparison.

### 2. The Methods Library for Others

The `methods/` directory is structured as a **reusable library** with clear separation of concerns:

**From `methods/README.md`:**
> "From the perspective of the manuscript, the only part of this library used directly is `neighborhood_sir.py`. The other scripts illustrate data processing workflows used to make the processed outputs in `../_data/`, but the raw data needed to run them is *not* included. They're here in this repository for reference purposes only, in case they help others apply these models and ideas to their own work."

This explicitly confirms your hypothesis. The methods are organized into three **generalizable workflows**:

1. **`epi_curves/`**: Logistic regression for classifying untested clinical cases as measles
2. **`age_at_inf/`**: Smoothing processes for age distribution of infections by birth cohort  
3. **`demography/`**: Multiple regression with post-stratification for survey data analysis
4. **`neighborhood_sir.py`**: The core SIR transmission model implementation

### 3. Key Architectural Evidence

**Data Structure Reveals Intent:**
- **`_data/`**: Contains processed, Nigeria-specific datasets (no raw data)
- **`pickle_jar/`**: Temporary serialized outputs for the reproduction workflow
- **`_plots/`**: Generated figures specific to the paper
- **`methods/`**: Clean, documented library code with minimal dependencies

**Code Organization Patterns:**
- Root scripts are **procedural and specific** (hardcoded state lists, specific file paths)
- Methods are **object-oriented and general** (classes like `NeighborhoodPosterior`, parameterized functions)

## What the Paper Reveals

From the research paper analysis, this work compares:
- **2019 Intensification of Routine Immunization (IRI)**: Targeted 9-month to 2-year-olds
- **Mass vaccination campaigns since 2010**: Targeted 9-month to 5-year-olds

**Key finding**: The 2019 IRI was **more than twice as effective per dose** as the mass campaigns, despite being smaller in scale.

The methodology involves:
- State-level stochastic transmission models for all 17 Southern Nigerian states
- Integration of surveillance data, household surveys, and vaccination records
- Bayesian inference to estimate vaccination efficacy and population susceptibility

## The "Is It Though?" Answer

**Yes, it absolutely is.** Your analysis is spot-on:

### For the Original Researcher:
```bash
python GeneratePriors.py      # ~20 minutes, all states
python GenerateStateSummary.py # Generates complete analysis
# → PDFs generated, results reproduced, done.
```

### For Other Researchers:
```python
from methods.neighborhood_sir import NeighborhoodPosterior
from methods.epi_curves import ConfRates
from methods.demography import BirthSeasonality
# → Apply these methods to your own data
```

## Implications for Repository Users

**If you're the original researcher**: The root-level workflow is your friend. Run the two main scripts and get comprehensive results.

**If you're applying these methods elsewhere**: Focus on the `methods/` directory. The `neighborhood_sir.py` file contains the core transmission model, while the other subdirectories provide data processing workflows you can adapt.

**If you're trying to understand the science**: Read the paper (`pdfs/intensification-paper.txt`) and examine how the methods implement the mathematical models described in the appendices.

## Bottom Line

This repository is a textbook example of **research code done right**:
- Complete reproducibility for the specific study
- Generalizable methods extracted for broader use
- Clear separation between "run my analysis" and "use my methods"
- Comprehensive documentation at both levels

Your intuition about the dual structure was absolutely correct. The researcher created a reproduction pipeline for their specific work while simultaneously packaging the underlying methods for the broader research community.

"""
Setup script for Streamlined Measles Transmission Modeling Methods
"""

from setuptools import setup, find_packages
from pathlib import Path

# Read the README file
this_directory = Path(__file__).parent
long_description = (this_directory / "README.md").read_text()

# Read requirements
requirements = []
with open(this_directory / "requirements.txt") as f:
    for line in f:
        line = line.strip()
        if line and not line.startswith('#'):
            requirements.append(line)

setup(
    name="measles-transmission-modeling",
    version="1.0.0",
    author="Streamlined from original methods by <PERSON><PERSON> et al.",
    author_email="",
    description="Modernized measles transmission modeling methods for vaccination campaign analysis",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/your-repo/measles-transmission-modeling",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Science/Research",
        "Topic :: Scientific/Engineering :: Medical Science Apps.",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
    ],
    python_requires=">=3.10",
    install_requires=requirements,
    entry_points={
        "console_scripts": [
            "measles-analysis=methods_streamlined.main:main",
        ],
    },
    include_package_data=True,
    package_data={
        "methods_streamlined": ["*.md", "*.txt"],
    },
)
